"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.bincountConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.bincountConfig = {
    kernelName: tfjs_1.Bincount,
    backendName: 'tensorflow',
    kernelFunc: function (_a) {
        var inputs = _a.inputs, backend = _a.backend, attrs = _a.attrs;
        var _b = inputs, x = _b.x, weights = _b.weights;
        var size = attrs.size;
        var nodeBackend = backend;
        var $size = (0, tfjs_1.scalar)(size, 'int32');
        var opAttrs = [(0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', weights.dtype)];
        var result = nodeBackend.executeSingleOutput(tfjs_1.Bincount, opAttrs, [x, $size, weights]);
        $size.dispose();
        return result;
    }
};
