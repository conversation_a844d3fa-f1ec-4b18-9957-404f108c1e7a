"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\");\n/* harmony import */ var _components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SyncStatusBanner */ \"(app-pages-browser)/./src/components/SyncStatusBanner.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/logger */ \"(app-pages-browser)/./src/lib/logger.ts\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/common/LoadingSpinner */ \"(app-pages-browser)/./src/components/common/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// This file defines the main layout for the MedTrack application, including the sidebar, top navigation, and main content area.\n\n\n\n\n\n\n\n\n\nconst MedTrackLayout = (param)=>{\n    let { children } = param;\n    _s();\n    // State management\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userMenuOpen, setUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationsOpen, setNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'learning'\n    ]);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Mock user data\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '1',\n        name: 'Dr. Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'Medical Student',\n        isAuthenticated: true\n    });\n    // Navigation structure\n    const navigationItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: Home,\n            href: '/dashboard'\n        },\n        {\n            id: 'learning',\n            label: 'Learning',\n            icon: GraduationCap,\n            href: '/learning',\n            children: [\n                {\n                    id: 'courses',\n                    label: 'My Courses',\n                    icon: BookOpen,\n                    href: '/courses',\n                    badge: 3\n                },\n                {\n                    id: 'progress',\n                    label: 'Progress Tracking',\n                    icon: BarChart3,\n                    href: '/progress'\n                },\n                {\n                    id: 'schedule',\n                    label: 'Study Schedule',\n                    icon: Calendar,\n                    href: '/schedule'\n                },\n                {\n                    id: 'quizzes',\n                    label: 'Quizzes & Tests',\n                    icon: Target,\n                    href: '/quizzes',\n                    badge: 2\n                }\n            ]\n        },\n        {\n            id: 'medical',\n            label: 'Medical Resources',\n            icon: _barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: '/medical',\n            children: [\n                {\n                    id: 'anatomy',\n                    label: 'Anatomy',\n                    icon: Activity,\n                    href: '/medical/anatomy'\n                },\n                {\n                    id: 'cardiology',\n                    label: 'Cardiology',\n                    icon: Heart,\n                    href: '/medical/cardiology'\n                },\n                {\n                    id: 'neurology',\n                    label: 'Neurology',\n                    icon: Brain,\n                    href: '/medical/neurology'\n                }\n            ]\n        },\n        {\n            id: 'community',\n            label: 'Community',\n            icon: Users,\n            href: '/community',\n            children: [\n                {\n                    id: 'discussions',\n                    label: 'Discussions',\n                    icon: Users,\n                    href: '/community/discussions'\n                },\n                {\n                    id: 'study-groups',\n                    label: 'Study Groups',\n                    icon: Users,\n                    href: '/community/study-groups'\n                }\n            ]\n        },\n        {\n            id: 'resources',\n            label: 'Resources',\n            icon: FileText,\n            href: '/resources'\n        },\n        {\n            id: 'achievements',\n            label: 'Achievements',\n            icon: Award,\n            href: '/achievements',\n            badge: 'New'\n        },\n        {\n            id: 'admin',\n            label: 'Admin',\n            icon: Settings,\n            href: '/admin',\n            children: [\n                {\n                    id: 'users',\n                    label: 'User Management',\n                    icon: Users,\n                    href: '/admin/users'\n                },\n                {\n                    id: 'roles',\n                    label: 'Role Management',\n                    icon: Shield,\n                    href: '/admin/roles'\n                },\n                {\n                    id: 'analytics',\n                    label: 'Analytics',\n                    icon: BarChart3,\n                    href: '/admin/analytics'\n                },\n                {\n                    id: 'settings',\n                    label: 'System Settings',\n                    icon: Settings,\n                    href: '/admin/settings'\n                }\n            ]\n        }\n    ];\n    // Mock notifications\n    const notifications = [\n        {\n            id: '1',\n            title: 'New Course Available',\n            message: 'Advanced Cardiology module is now live',\n            time: '5 min ago',\n            unread: true\n        },\n        {\n            id: '2',\n            title: 'Assignment Due',\n            message: 'Anatomy quiz due tomorrow',\n            time: '2 hours ago',\n            unread: true\n        },\n        {\n            id: '3',\n            title: 'Study Group Meeting',\n            message: 'Cardiology study group starts in 1 hour',\n            time: '1 day ago',\n            unread: false\n        }\n    ];\n    // Theme toggle\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'light' ? 'dark' : 'light');\n    };\n    // Sidebar section toggle\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>prev.includes(sectionId) ? prev.filter((id)=>id !== sectionId) : [\n                ...prev,\n                sectionId\n            ]);\n    };\n    // Handle outside clicks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"MedTrackLayout.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (!target.closest('.user-menu') && !target.closest('.user-menu-button')) {\n                        setUserMenuOpen(false);\n                    }\n                    if (!target.closest('.notifications-menu') && !target.closest('.notifications-button')) {\n                        setNotificationsOpen(false);\n                    }\n                }\n            }[\"MedTrackLayout.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // Simulate loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"MedTrackLayout.useEffect.timer\": ()=>setIsLoading(false)\n            }[\"MedTrackLayout.useEffect.timer\"], 1000);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // ServiceWorker registration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"MedTrackLayout.useEffect\": (_registration)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.info('ServiceWorker registration successful');\n                    }\n                }[\"MedTrackLayout.useEffect\"]).catch({\n                    \"MedTrackLayout.useEffect\": (err)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.error('ServiceWorker registration failed:', err);\n                    }\n                }[\"MedTrackLayout.useEffect\"]);\n            }\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 263,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"\".concat(sidebarOpen ? 'block' : 'hidden', \" lg:block\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"lg:hidden fixed top-4 left-4 z-20\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            \"aria-expanded\": String(sidebarOpen),\n                            \"aria-label\": \"Toggle sidebar navigation\",\n                            title: \"Toggle Navigation\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Menu, {\n                                className: \"h-6 w-6\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out\\n            \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full', \"\\n            lg:translate-x-0 lg:static lg:inset-0\\n            \").concat(sidebarCollapsed && !sidebarOpen ? 'lg:w-16' : 'w-64', \"\\n            \").concat(theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200', \"\\n            border-r flex flex-col\\n          \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: \"MedTrack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                            children: \"Medical Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: \"lg:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex-1 overflow-y-auto py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                                                item: item\n                                            }, item.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t border-gray-200 dark:border-gray-700\",\n                                    children: (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs truncate \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                        children: user.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"bg-white dark:bg-gray-800 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                        className: \"text-gray-500 hover:text-gray-600 dark:text-gray-400\",\n                                        \"aria-expanded\": String(!sidebarCollapsed),\n                                        \"aria-label\": \"Toggle sidebar collapse\",\n                                        title: \"Toggle Sidebar\",\n                                        children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Menu, {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search courses, topics...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 pr-4 py-2 w-64 rounded-lg border \".concat(theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500', \" focus:ring-2 focus:ring-blue-500 focus:border-blue-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleTheme,\n                                                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sun, {\n                                                    className: \"h-5 w-5 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Moon, {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setNotificationsOpen(!notificationsOpen),\n                                                        className: \"notifications-button relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        title: \"Toggle notifications\",\n                                                        \"aria-label\": \"Toggle notifications\",\n                                                        \"aria-expanded\": notificationsOpen,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Bell, {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\",\n                                                                \"aria-label\": \"New notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    notificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"notifications-menu absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"Notifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-80 overflow-y-auto\",\n                                                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 rounded-full mt-2 \".concat(notification.unread ? 'bg-blue-500' : 'bg-gray-300')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 416,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: notification.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 418,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                                            children: notification.message\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 419,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                                            children: notification.time\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 420,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 417,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, notification.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setUserMenuOpen(!userMenuOpen),\n                                                        className: \"user-menu-button flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                                                    className: \"h-4 w-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronDown, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    userMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"user-menu absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Settings, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Settings\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 452,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HelpCircle, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Help & Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                        className: \"my-2 border-gray-200 dark:border-gray-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogOut, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Sign Out\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 461,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 sm:p-6 lg:p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 35\n                                    }, void 0),\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 sm:px-6 lg:px-8 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"MedTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"\\xa9 2025 All rights reserved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mt-2 sm:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MedTrackLayout, \"+Rb3zV2sCTVBQS6YAQiNEhnRBMs=\");\n_c = MedTrackLayout;\nconst RootLayout = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MedTrackLayout, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__.SyncStatusBanner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 9\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 512,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 511,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = RootLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RootLayout);\nvar _c, _c1;\n$RefreshReg$(_c, \"MedTrackLayout\");\n$RefreshReg$(_c1, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cbbedc6c17b6\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXG1lZGljYWxcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjYmJlZGM2YzE3YjZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});