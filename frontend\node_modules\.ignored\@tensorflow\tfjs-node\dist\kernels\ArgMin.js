"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.argMinConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.argMinConfig = {
    kernelName: tfjs_1.ArgMin,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var x = args.inputs.x;
        var backend = args.backend;
        var axis = args.attrs.axis;
        var toDispose = [];
        var xInput = x;
        if (x.dtype === 'bool') {
            xInput = x.toInt();
            toDispose.push(xInput);
        }
        var axisScalar = (0, tfjs_1.scalar)(axis, 'int32');
        toDispose.push(axisScalar);
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', xInput.dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Tidx', 'int32'),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('output_type', 'int32')
        ];
        var res = backend.executeSingleOutput(tfjs_1.ArgMin, opAttrs, [xInput, axisScalar]);
        toDispose.forEach(function (t) { return t.dispose(); });
        return res;
    }
};
