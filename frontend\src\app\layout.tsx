'use client';

// This file defines the main layout for the MedTrack application, including the sidebar, top navigation, and main content area.

import React, { useState, useEffect, ReactNode, Suspense } from 'react';
import { 
  Menu,
  X,
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  Sun,
  Moon,
  ChevronDown,
  ChevronRight,
  Home,
  BookOpen,
  BarChart3,
  Calendar,
  Users,
  FileText,
  Award,
  HelpCircle,
  Stethoscope,
  Activity,
  Brain,
  Heart,
  GraduationCap,
  Clock,
  Target,
  Zap,
  Shield
} from 'lucide-react';
import { Providers } from './providers';
import '../styles/globals.css';
import { SyncStatusBanner } from '@/components/SyncStatusBanner';
import { Toaster } from 'react-hot-toast';
import { logger } from '@/lib/logger';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';

// Types
interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  badge?: string | number;
  children?: NavigationItem[];
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  isAuthenticated: boolean;
}

interface LayoutProps {
  children: ReactNode;
}

const MedTrackLayout: React.FC<LayoutProps> = ({ children }) => {
  // State management
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [expandedSections, setExpandedSections] = useState<string[]>(['learning']);

  // Mock user data
  const [user] = useState<User>({
    id: '1',
    name: 'Dr. Sarah Johnson',
    email: '<EMAIL>',
    role: 'Medical Student',
    isAuthenticated: true
  });

  // Navigation structure
  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      href: '/dashboard'
    },
    {
      id: 'learning',
      label: 'Learning',
      icon: GraduationCap,
      href: '/learning',
      children: [
        {
          id: 'courses',
          label: 'My Courses',
          icon: BookOpen,
          href: '/courses',
          badge: 3
        },
        {
          id: 'progress',
          label: 'Progress Tracking',
          icon: BarChart3,
          href: '/progress'
        },
        {
          id: 'schedule',
          label: 'Study Schedule',
          icon: Calendar,
          href: '/schedule'
        },
        {
          id: 'quizzes',
          label: 'Quizzes & Tests',
          icon: Target,
          href: '/quizzes',
          badge: 2
        }
      ]
    },
    {
      id: 'medical',
      label: 'Medical Resources',
      icon: Stethoscope,
      href: '/medical',
      children: [
        {
          id: 'anatomy',
          label: 'Anatomy',
          icon: Activity,
          href: '/medical/anatomy'
        },
        {
          id: 'cardiology',
          label: 'Cardiology',
          icon: Heart,
          href: '/medical/cardiology'
        },
        {
          id: 'neurology',
          label: 'Neurology',
          icon: Brain,
          href: '/medical/neurology'
        }
      ]
    },
    {
      id: 'community',
      label: 'Community',
      icon: Users,
      href: '/community',
      children: [
        {
          id: 'discussions',
          label: 'Discussions',
          icon: Users,
          href: '/community/discussions'
        },
        {
          id: 'study-groups',
          label: 'Study Groups',
          icon: Users,
          href: '/community/study-groups'
        }
      ]
    },
    {
      id: 'resources',
      label: 'Resources',
      icon: FileText,
      href: '/resources'
    },
    {
      id: 'achievements',
      label: 'Achievements',
      icon: Award,
      href: '/achievements',
      badge: 'New'
    },
    {
      id: 'admin',
      label: 'Admin',
      icon: Settings,
      href: '/admin',
      children: [
        {
          id: 'users',
          label: 'User Management',
          icon: Users,
          href: '/admin/users'
        },
        {
          id: 'roles',
          label: 'Role Management',
          icon: Shield,
          href: '/admin/roles'
        },
        {
          id: 'analytics',
          label: 'Analytics',
          icon: BarChart3,
          href: '/admin/analytics'
        },
        {
          id: 'settings',
          label: 'System Settings',
          icon: Settings,
          href: '/admin/settings'
        }
      ]
    }
  ];

  // Mock notifications
  const notifications = [
    {
      id: '1',
      title: 'New Course Available',
      message: 'Advanced Cardiology module is now live',
      time: '5 min ago',
      unread: true
    },
    {
      id: '2',
      title: 'Assignment Due',
      message: 'Anatomy quiz due tomorrow',
      time: '2 hours ago',
      unread: true
    },
    {
      id: '3',
      title: 'Study Group Meeting',
      message: 'Cardiology study group starts in 1 hour',
      time: '1 day ago',
      unread: false
    }
  ];

  // Theme toggle
  const toggleTheme = () => {
    // Implementation of toggleTheme
  };

  // Sidebar section toggle
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  // Handle outside clicks
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.user-menu') && !target.closest('.user-menu-button')) {
        setUserMenuOpen(false);
      }
      if (!target.closest('.notifications-menu') && !target.closest('.notifications-button')) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  // ServiceWorker registration
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((_registration) => {
          logger.info('ServiceWorker registration successful');
        })
        .catch((err) => {
          logger.error('ServiceWorker registration failed:', err);
        });
    }
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        {/* Sidebar */}
        <nav className={`${sidebarOpen ? 'block' : 'hidden'} lg:block`}>
          <button
            className="lg:hidden fixed top-4 left-4 z-20"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            aria-expanded={String(sidebarOpen)}
            aria-label="Toggle sidebar navigation"
            title="Toggle Navigation"
          >
            <Menu className="h-6 w-6" aria-hidden="true" />
          </button>

          <div className={`
            fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out
            ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            lg:translate-x-0 lg:static lg:inset-0
            ${sidebarCollapsed && !sidebarOpen ? 'lg:w-16' : 'w-64'}
            ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
            border-r flex flex-col
          `}>
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <Stethoscope className="h-8 w-8 text-blue-600" />
                {(!sidebarCollapsed || sidebarOpen) && (
                  <div>
                    <h1 className="text-xl font-bold">MedTrack</h1>
                    <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>Medical Education</p>
                  </div>
                )}
              </div>
              
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 overflow-y-auto py-4">
              <div className="space-y-1">
                {navigationItems.map(item => (
                  <NavigationItem key={item.id} item={item} />
                ))}
              </div>
            </nav>

            {/* Sidebar Footer */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              {(!sidebarCollapsed || sidebarOpen) && (
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{user.name}</p>
                    <p className={`text-xs truncate ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      {user.role}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <header className="bg-white dark:bg-gray-800 shadow-sm">
            <div className="flex items-center justify-between p-4">
              <button
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="text-gray-500 hover:text-gray-600 dark:text-gray-400"
                aria-expanded={String(!sidebarCollapsed)}
                aria-label="Toggle sidebar collapse"
                title="Toggle Sidebar"
              >
                {sidebarCollapsed ? (
                  <Menu className="h-6 w-6" aria-hidden="true" />
                ) : (
                  <X className="h-6 w-6" aria-hidden="true" />
                )}
              </button>

              {/* Search */}
              <div className="relative hidden sm:block">
                <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} />
                <input
                  type="text"
                  placeholder="Search courses, topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`pl-10 pr-4 py-2 w-64 rounded-lg border ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                />
              </div>

              {/* User Menu and Notifications */}
              <div className="flex items-center space-x-4">
                {/* Theme Toggle */}
                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  {theme === 'dark' ? (
                    <Sun className="h-5 w-5 text-yellow-500" />
                  ) : (
                    <Moon className="h-5 w-5 text-gray-600" />
                  )}
                </button>

                {/* Notifications */}
                <div className="relative">
                  <button
                    onClick={() => setNotificationsOpen(!notificationsOpen)}
                    className="notifications-button relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    title="Toggle notifications"
                    aria-label="Toggle notifications"
                    aria-expanded={notificationsOpen}
                  >
                    <Bell className="h-5 w-5" />
                    <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full" aria-label="New notifications"></span>
                  </button>

                  {notificationsOpen && (
                    <div className="notifications-menu absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 className="font-semibold">Notifications</h3>
                      </div>
                      <div className="max-h-80 overflow-y-auto">
                        {notifications.map(notification => (
                          <div key={notification.id} className="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <div className="flex items-start space-x-3">
                              <div className={`w-2 h-2 rounded-full mt-2 ${notification.unread ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                              <div className="flex-1">
                                <p className="text-sm font-medium">{notification.title}</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{notification.message}</p>
                                <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* User Menu */}
                <div className="relative">
                  <button
                    onClick={() => setUserMenuOpen(!userMenuOpen)}
                    className="user-menu-button flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <User className="h-4 w-4 text-white" />
                    </div>
                    <ChevronDown className="h-4 w-4" />
                  </button>

                  {userMenuOpen && (
                    <div className="user-menu absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                        <p className="font-medium text-sm">{user.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{user.email}</p>
                      </div>
                      <div className="p-2">
                        <button className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left">
                          <Settings className="h-4 w-4" />
                          <span className="text-sm">Settings</span>
                        </button>
                        <button className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left">
                          <HelpCircle className="h-4 w-4" />
                          <span className="text-sm">Help & Support</span>
                        </button>
                        <hr className="my-2 border-gray-200 dark:border-gray-700" />
                        <button className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left text-red-600">
                          <LogOut className="h-4 w-4" />
                          <span className="text-sm">Sign Out</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-auto">
            <div className="p-4 sm:p-6 lg:p-8">
              <Suspense fallback={<LoadingSpinner />}>
                {children}
              </Suspense>
            </div>
          </main>

          {/* Footer */}
          <footer className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col sm:flex-row justify-between items-center">
              <div className="flex items-center space-x-4">
                <Stethoscope className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium">MedTrack</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  © 2025 All rights reserved
                </span>
              </div>
              <div className="flex items-center space-x-4 mt-2 sm:mt-0">
                <a href="#" className="text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  Privacy Policy
                </a>
                <a href="#" className="text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  Terms of Service
                </a>
                <a href="#" className="text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                  Support
                </a>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </ErrorBoundary>
  );
};

const RootLayout = ({ children }: LayoutProps) => {
  return (
    <Providers>
      <MedTrackLayout>
        <Toaster position="top-right" />
        <SyncStatusBanner />
        {children}
      </MedTrackLayout>
    </Providers>
  );
};

export default RootLayout;