"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChevronRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.323.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronRight\", [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC4zMjMuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLXJpZ2h0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0scUJBQWUsZ0VBQWdCLENBQUMsY0FBZ0I7SUFDcEQ7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWlCO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUMvQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzcmNcXGljb25zXFxjaGV2cm9uLXJpZ2h0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hldnJvblJpZ2h0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnRPU0F4T0NBMkxUWXROaTAySWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi1yaWdodFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25SaWdodCA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZXZyb25SaWdodCcsIFtcbiAgWydwYXRoJywgeyBkOiAnbTkgMTggNi02LTYtNicsIGtleTogJ210aGh3cScgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvblJpZ2h0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\");\n/* harmony import */ var _components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SyncStatusBanner */ \"(app-pages-browser)/./src/components/SyncStatusBanner.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/logger */ \"(app-pages-browser)/./src/lib/logger.ts\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/common/LoadingSpinner */ \"(app-pages-browser)/./src/components/common/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n// This file defines the main layout for the MedTrack application, including the sidebar, top navigation, and main content area.\n\n\n\n\n\n\n\n\n\n// Navigation Item Component\nconst NavigationItem = (param)=>{\n    let { item } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleClick = ()=>{\n        if (item.children) {\n            setIsExpanded(!isExpanded);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                onClick: handleClick,\n                className: \"w-full flex items-center justify-between px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                children: item.badge\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4 transition-transform \".concat(isExpanded ? 'rotate-90' : '')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            item.children && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-6 mt-1 space-y-1\",\n                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                        item: child\n                    }, child.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationItem, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c = NavigationItem;\nconst MedTrackLayout = (param)=>{\n    let { children } = param;\n    _s1();\n    // State management\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userMenuOpen, setUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationsOpen, setNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'learning'\n    ]);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Mock user data\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '1',\n        name: 'Dr. Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'Medical Student',\n        isAuthenticated: true\n    });\n    // Navigation structure\n    const navigationItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: '/dashboard'\n        },\n        {\n            id: 'learning',\n            label: 'Learning',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            href: '/learning',\n            children: [\n                {\n                    id: 'courses',\n                    label: 'My Courses',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: '/courses',\n                    badge: 3\n                },\n                {\n                    id: 'progress',\n                    label: 'Progress Tracking',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    href: '/progress'\n                },\n                {\n                    id: 'schedule',\n                    label: 'Study Schedule',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: '/schedule'\n                },\n                {\n                    id: 'quizzes',\n                    label: 'Quizzes & Tests',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    href: '/quizzes',\n                    badge: 2\n                }\n            ]\n        },\n        {\n            id: 'medical',\n            label: 'Medical Resources',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            href: '/medical',\n            children: [\n                {\n                    id: 'anatomy',\n                    label: 'Anatomy',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    href: '/medical/anatomy'\n                },\n                {\n                    id: 'cardiology',\n                    label: 'Cardiology',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    href: '/medical/cardiology'\n                },\n                {\n                    id: 'neurology',\n                    label: 'Neurology',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    href: '/medical/neurology'\n                }\n            ]\n        },\n        {\n            id: 'community',\n            label: 'Community',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            href: '/community',\n            children: [\n                {\n                    id: 'discussions',\n                    label: 'Discussions',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: '/community/discussions'\n                },\n                {\n                    id: 'study-groups',\n                    label: 'Study Groups',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: '/community/study-groups'\n                }\n            ]\n        },\n        {\n            id: 'resources',\n            label: 'Resources',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            href: '/resources'\n        },\n        {\n            id: 'achievements',\n            label: 'Achievements',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            href: '/achievements',\n            badge: 'New'\n        },\n        {\n            id: 'admin',\n            label: 'Admin',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            href: '/admin',\n            children: [\n                {\n                    id: 'users',\n                    label: 'User Management',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: '/admin/users'\n                },\n                {\n                    id: 'roles',\n                    label: 'Role Management',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                    href: '/admin/roles'\n                },\n                {\n                    id: 'analytics',\n                    label: 'Analytics',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    href: '/admin/analytics'\n                },\n                {\n                    id: 'settings',\n                    label: 'System Settings',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    href: '/admin/settings'\n                }\n            ]\n        }\n    ];\n    // Mock notifications\n    const notifications = [\n        {\n            id: '1',\n            title: 'New Course Available',\n            message: 'Advanced Cardiology module is now live',\n            time: '5 min ago',\n            unread: true\n        },\n        {\n            id: '2',\n            title: 'Assignment Due',\n            message: 'Anatomy quiz due tomorrow',\n            time: '2 hours ago',\n            unread: true\n        },\n        {\n            id: '3',\n            title: 'Study Group Meeting',\n            message: 'Cardiology study group starts in 1 hour',\n            time: '1 day ago',\n            unread: false\n        }\n    ];\n    // Theme toggle\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'light' ? 'dark' : 'light');\n    };\n    // Sidebar section toggle\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>prev.includes(sectionId) ? prev.filter((id)=>id !== sectionId) : [\n                ...prev,\n                sectionId\n            ]);\n    };\n    // Handle outside clicks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"MedTrackLayout.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (!target.closest('.user-menu') && !target.closest('.user-menu-button')) {\n                        setUserMenuOpen(false);\n                    }\n                    if (!target.closest('.notifications-menu') && !target.closest('.notifications-button')) {\n                        setNotificationsOpen(false);\n                    }\n                }\n            }[\"MedTrackLayout.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // Simulate loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"MedTrackLayout.useEffect.timer\": ()=>setIsLoading(false)\n            }[\"MedTrackLayout.useEffect.timer\"], 1000);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // ServiceWorker registration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"MedTrackLayout.useEffect\": (_registration)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.info('ServiceWorker registration successful');\n                    }\n                }[\"MedTrackLayout.useEffect\"]).catch({\n                    \"MedTrackLayout.useEffect\": (err)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.error('ServiceWorker registration failed:', err);\n                    }\n                }[\"MedTrackLayout.useEffect\"]);\n            }\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"\".concat(sidebarOpen ? 'block' : 'hidden', \" lg:block\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"lg:hidden fixed top-4 left-4 z-20\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            \"aria-expanded\": String(sidebarOpen),\n                            \"aria-label\": \"Toggle sidebar navigation\",\n                            title: \"Toggle Navigation\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"h-6 w-6\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out\\n            \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full', \"\\n            lg:translate-x-0 lg:static lg:inset-0\\n            \").concat(sidebarCollapsed && !sidebarOpen ? 'lg:w-16' : 'w-64', \"\\n            \").concat(theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200', \"\\n            border-r flex flex-col\\n          \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: \"MedTrack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                            children: \"Medical Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: \"lg:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex-1 overflow-y-auto py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                                                item: item\n                                            }, item.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t border-gray-200 dark:border-gray-700\",\n                                    children: (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs truncate \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                        children: user.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"bg-white dark:bg-gray-800 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                        className: \"text-gray-500 hover:text-gray-600 dark:text-gray-400\",\n                                        \"aria-expanded\": String(!sidebarCollapsed),\n                                        \"aria-label\": \"Toggle sidebar collapse\",\n                                        title: \"Toggle Sidebar\",\n                                        children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search courses, topics...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 pr-4 py-2 w-64 rounded-lg border \".concat(theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500', \" focus:ring-2 focus:ring-blue-500 focus:border-blue-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleTheme,\n                                                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setNotificationsOpen(!notificationsOpen),\n                                                        className: \"notifications-button relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        title: \"Toggle notifications\",\n                                                        \"aria-label\": \"Toggle notifications\",\n                                                        \"aria-expanded\": notificationsOpen,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\",\n                                                                \"aria-label\": \"New notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    notificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"notifications-menu absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"Notifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-80 overflow-y-auto\",\n                                                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 rounded-full mt-2 \".concat(notification.unread ? 'bg-blue-500' : 'bg-gray-300')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 489,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: notification.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 491,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                                            children: notification.message\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 492,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                                            children: notification.time\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 493,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 490,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, notification.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setUserMenuOpen(!userMenuOpen),\n                                                        className: \"user-menu-button flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    userMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"user-menu absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Settings\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Help & Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                        className: \"my-2 border-gray-200 dark:border-gray-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 532,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Sign Out\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 sm:p-6 lg:p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 35\n                                    }, void 0),\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 sm:px-6 lg:px-8 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"MedTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"\\xa9 2025 All rights reserved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mt-2 sm:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(MedTrackLayout, \"+Rb3zV2sCTVBQS6YAQiNEhnRBMs=\");\n_c1 = MedTrackLayout;\nconst RootLayout = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MedTrackLayout, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__.SyncStatusBanner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 584,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 583,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = RootLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RootLayout);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NavigationItem\");\n$RefreshReg$(_c1, \"MedTrackLayout\");\n$RefreshReg$(_c2, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"397172442a8a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXG1lZGljYWxcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzOTcxNzI0NDJhOGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});