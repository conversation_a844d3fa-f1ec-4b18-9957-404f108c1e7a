"use strict";
/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.encodeInt32ArrayAsInt64 = exports.Int64Scalar = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var os_1 = require("os");
var INT32_MAX = **********;
/**
 * Node.js-specific tensor type: int64-type scalar.
 *
 * This class is created for a specific purpose: to support
 * writing `step`s to TensorBoard via op-kernel bindings.
 * `step` is required to have an int64 dtype, but TensorFlow.js
 * (tfjs-core) doesn't have a built-in int64 dtype. This is
 * related to a lack of `Int64Array` or `Uint64Array` typed
 * array in basic JavaScript.
 *
 * This class is introduced as a workaround.
 */
var Int64Scalar = /** @class */ (function () {
    function Int64Scalar(value) {
        this.value = value;
        this.dtype = 'int64';
        this.rank = 1;
        // The reason why we need to check endianness of the machine here is
        // negative int64 values and the way in which we represent them
        // using Int32Arrays in JavaScript. We represent each int64 value with
        // two consecutive elements of an Int32Array. For positive values,
        // the high part is simply zero; for negative values, the high part
        // should be -1. The ordering of the low and high parts assumes
        // little endian (i.e., least significant digits appear first).
        // This assumption is checked by the lines below.
        if (Int64Scalar.endiannessOkay_ == null) {
            if ((0, os_1.endianness)() !== 'LE') {
                throw new Error("Int64Scalar does not support endianness of this machine: " +
                    "".concat((0, os_1.endianness)()));
            }
            Int64Scalar.endiannessOkay_ = true;
        }
        tfjs_1.util.assert(value > -INT32_MAX && value < INT32_MAX - 1, function () {
            return "Got a value outside of the bound of values supported for int64 " +
                "dtype ([-".concat(INT32_MAX, ", ").concat(INT32_MAX - 1, "]): ").concat(value);
        });
        tfjs_1.util.assert(Number.isInteger(value), function () { return "Expected value to be an integer, but got ".concat(value); });
        // We use two int32 elements to represent a int64 value. This assumes
        // little endian, which is checked above.
        var highPart = value >= 0 ? 0 : -1;
        var lowPart = value % INT32_MAX;
        this.valueArray_ = new Int32Array([lowPart, highPart]);
    }
    Object.defineProperty(Int64Scalar.prototype, "shape", {
        get: function () {
            return [];
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Int64Scalar.prototype, "valueArray", {
        /** Get the Int32Array that represents the int64 value. */
        get: function () {
            return this.valueArray_;
        },
        enumerable: false,
        configurable: true
    });
    return Int64Scalar;
}());
exports.Int64Scalar = Int64Scalar;
/**
 * This method encodes a Int32Array as Int64 layout in order to create TF_INT64
 * tensor through binding.
 */
function encodeInt32ArrayAsInt64(value) {
    if ((0, os_1.endianness)() !== 'LE') {
        throw new Error("Int64Scalar does not support endianness of this machine: " +
            "".concat((0, os_1.endianness)()));
    }
    var buffer = new Int32Array(value.length * 2);
    for (var i = 0; i < value.length; i++) {
        buffer[i * 2] = value[i];
    }
    return buffer;
}
exports.encodeInt32ArrayAsInt64 = encodeInt32ArrayAsInt64;
