"use strict";
/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
var int64_tensors_1 = require("./int64_tensors");
describe('int64 tensors', function () {
    it('positive value', function () {
        var x = new int64_tensors_1.Int64Scalar(42);
        expect(x.dtype).toEqual('int64');
        var valueArray = x.valueArray;
        expect(valueArray.constructor.name).toEqual('Int32Array');
        expect(valueArray.length).toEqual(2);
        expect(valueArray[0]).toEqual(42);
        expect(valueArray[1]).toEqual(0);
    });
    it('zero value', function () {
        var x = new int64_tensors_1.Int64Scalar(0);
        expect(x.dtype).toEqual('int64');
        var valueArray = x.valueArray;
        expect(valueArray.constructor.name).toEqual('Int32Array');
        expect(valueArray.length).toEqual(2);
        expect(valueArray[0]).toEqual(0);
        expect(valueArray[1]).toEqual(0);
    });
    it('negative value', function () {
        var x = new int64_tensors_1.Int64Scalar(-3);
        expect(x.dtype).toEqual('int64');
        var valueArray = x.valueArray;
        expect(valueArray.constructor.name).toEqual('Int32Array');
        expect(valueArray.length).toEqual(2);
        expect(valueArray[0]).toEqual(-3);
        expect(valueArray[1]).toEqual(-1);
    });
    it('Non-integer value leads to error', function () {
        expect(function () { return new int64_tensors_1.Int64Scalar(0.4); }).toThrowError(/integer/);
        expect(function () { return new int64_tensors_1.Int64Scalar(-3.2); }).toThrowError(/integer/);
    });
    it('Out-of-bound value leads to error', function () {
        expect(function () { return new int64_tensors_1.Int64Scalar(2147483648); }).toThrowError(/bound/);
        expect(function () { return new int64_tensors_1.Int64Scalar(2147483648 * 2); }).toThrowError(/bound/);
        expect(function () { return new int64_tensors_1.Int64Scalar(-2147483648 - 1); }).toThrowError(/bound/);
    });
    it('encode int32array as int64 layout', function () {
        var input = Int32Array.from([2, 10]);
        var valueArray = (0, int64_tensors_1.encodeInt32ArrayAsInt64)(input);
        expect(valueArray.length).toEqual(4);
        expect(valueArray[0]).toEqual(2);
        expect(valueArray[1]).toEqual(0);
        expect(valueArray[2]).toEqual(10);
        expect(valueArray[3]).toEqual(0);
    });
});
