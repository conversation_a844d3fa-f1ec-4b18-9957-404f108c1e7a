export default function Head() {
  return (
    <>
      <title>Medical Education Platform</title>
      <meta name="description" content="Advanced medical education platform for healthcare professionals" />
      <meta name="keywords" content="medical, education, healthcare, learning, courses" />
      {/* Open Graph */}
      <meta property="og:title" content="Medical Education Platform | Medical Learning Platform" />
      <meta property="og:description" content="Advanced medical education platform for healthcare professionals" />
      <meta property="og:type" content="website" />
      <meta property="og:image" content="/images/og-image.jpg" />
      <meta property="og:site_name" content="Medical Learning Platform" />
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content="Medical Education Platform | Medical Learning Platform" />
      <meta name="twitter:description" content="Advanced medical education platform for healthcare professionals" />
      <meta name="twitter:image" content="/images/og-image.jpg" />
      {/* Robots (noindex example, remove if not needed) */}
      {/* <meta name="robots" content="noindex,nofollow" /> */}
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />
      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="theme-color" content="#4F46E5" />
      {/* Add any global scripts here using <script async src="..."></script> */}
    </>
  );
} 