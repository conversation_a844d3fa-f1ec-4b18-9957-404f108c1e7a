"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,ChevronRight,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\");\n/* harmony import */ var _components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SyncStatusBanner */ \"(app-pages-browser)/./src/components/SyncStatusBanner.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/logger */ \"(app-pages-browser)/./src/lib/logger.ts\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/common/LoadingSpinner */ \"(app-pages-browser)/./src/components/common/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n// This file defines the main layout for the MedTrack application, including the sidebar, top navigation, and main content area.\n\n\n\n\n\n\n\n\n\n// Navigation Item Component\nconst NavigationItem = (param)=>{\n    let { item } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleClick = ()=>{\n        if (item.children) {\n            setIsExpanded(!isExpanded);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                onClick: handleClick,\n                className: \"w-full flex items-center justify-between px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                children: item.badge\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4 transition-transform \".concat(isExpanded ? 'rotate-90' : '')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            item.children && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-6 mt-1 space-y-1\",\n                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                        item: child\n                    }, child.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationItem, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c = NavigationItem;\nconst MedTrackLayout = (param)=>{\n    let { children } = param;\n    _s1();\n    // State management\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userMenuOpen, setUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationsOpen, setNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'learning'\n    ]);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Mock user data\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '1',\n        name: 'Dr. Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'Medical Student',\n        isAuthenticated: true\n    });\n    // Navigation structure\n    const navigationItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: '/dashboard'\n        },\n        {\n            id: 'learning',\n            label: 'Learning',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            href: '/learning',\n            children: [\n                {\n                    id: 'courses',\n                    label: 'My Courses',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: '/courses',\n                    badge: 3\n                },\n                {\n                    id: 'progress',\n                    label: 'Progress Tracking',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    href: '/progress'\n                },\n                {\n                    id: 'schedule',\n                    label: 'Study Schedule',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: '/schedule'\n                },\n                {\n                    id: 'quizzes',\n                    label: 'Quizzes & Tests',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    href: '/quizzes',\n                    badge: 2\n                }\n            ]\n        },\n        {\n            id: 'medical',\n            label: 'Medical Resources',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            href: '/medical',\n            children: [\n                {\n                    id: 'anatomy',\n                    label: 'Anatomy',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    href: '/medical/anatomy'\n                },\n                {\n                    id: 'cardiology',\n                    label: 'Cardiology',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    href: '/medical/cardiology'\n                },\n                {\n                    id: 'neurology',\n                    label: 'Neurology',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    href: '/medical/neurology'\n                }\n            ]\n        },\n        {\n            id: 'community',\n            label: 'Community',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            href: '/community',\n            children: [\n                {\n                    id: 'discussions',\n                    label: 'Discussions',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: '/community/discussions'\n                },\n                {\n                    id: 'study-groups',\n                    label: 'Study Groups',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: '/community/study-groups'\n                }\n            ]\n        },\n        {\n            id: 'resources',\n            label: 'Resources',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            href: '/resources'\n        },\n        {\n            id: 'achievements',\n            label: 'Achievements',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            href: '/achievements',\n            badge: 'New'\n        },\n        {\n            id: 'admin',\n            label: 'Admin',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            href: '/admin',\n            children: [\n                {\n                    id: 'users',\n                    label: 'User Management',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    href: '/admin/users'\n                },\n                {\n                    id: 'roles',\n                    label: 'Role Management',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                    href: '/admin/roles'\n                },\n                {\n                    id: 'analytics',\n                    label: 'Analytics',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    href: '/admin/analytics'\n                },\n                {\n                    id: 'settings',\n                    label: 'System Settings',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    href: '/admin/settings'\n                }\n            ]\n        }\n    ];\n    // Mock notifications\n    const notifications = [\n        {\n            id: '1',\n            title: 'New Course Available',\n            message: 'Advanced Cardiology module is now live',\n            time: '5 min ago',\n            unread: true\n        },\n        {\n            id: '2',\n            title: 'Assignment Due',\n            message: 'Anatomy quiz due tomorrow',\n            time: '2 hours ago',\n            unread: true\n        },\n        {\n            id: '3',\n            title: 'Study Group Meeting',\n            message: 'Cardiology study group starts in 1 hour',\n            time: '1 day ago',\n            unread: false\n        }\n    ];\n    // Theme toggle\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'light' ? 'dark' : 'light');\n    };\n    // Sidebar section toggle\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>prev.includes(sectionId) ? prev.filter((id)=>id !== sectionId) : [\n                ...prev,\n                sectionId\n            ]);\n    };\n    // Handle outside clicks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"MedTrackLayout.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (!target.closest('.user-menu') && !target.closest('.user-menu-button')) {\n                        setUserMenuOpen(false);\n                    }\n                    if (!target.closest('.notifications-menu') && !target.closest('.notifications-button')) {\n                        setNotificationsOpen(false);\n                    }\n                }\n            }[\"MedTrackLayout.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // Simulate loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"MedTrackLayout.useEffect.timer\": ()=>setIsLoading(false)\n            }[\"MedTrackLayout.useEffect.timer\"], 1000);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // ServiceWorker registration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"MedTrackLayout.useEffect\": (_registration)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.info('ServiceWorker registration successful');\n                    }\n                }[\"MedTrackLayout.useEffect\"]).catch({\n                    \"MedTrackLayout.useEffect\": (err)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.error('ServiceWorker registration failed:', err);\n                    }\n                }[\"MedTrackLayout.useEffect\"]);\n            }\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"\".concat(sidebarOpen ? 'block' : 'hidden', \" lg:block\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"lg:hidden fixed top-4 left-4 z-20\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            \"aria-expanded\": String(sidebarOpen),\n                            \"aria-label\": \"Toggle sidebar navigation\",\n                            title: \"Toggle Navigation\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"h-6 w-6\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out\\n            \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full', \"\\n            lg:translate-x-0 lg:static lg:inset-0\\n            \").concat(sidebarCollapsed && !sidebarOpen ? 'lg:w-16' : 'w-64', \"\\n            \").concat(theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200', \"\\n            border-r flex flex-col\\n          \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: \"MedTrack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                            children: \"Medical Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: \"lg:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex-1 overflow-y-auto py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                                                item: item\n                                            }, item.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t border-gray-200 dark:border-gray-700\",\n                                    children: (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs truncate \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                        children: user.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"bg-white dark:bg-gray-800 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                        className: \"text-gray-500 hover:text-gray-600 dark:text-gray-400\",\n                                        \"aria-expanded\": String(!sidebarCollapsed),\n                                        \"aria-label\": \"Toggle sidebar collapse\",\n                                        title: \"Toggle Sidebar\",\n                                        children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search courses, topics...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 pr-4 py-2 w-64 rounded-lg border \".concat(theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500', \" focus:ring-2 focus:ring-blue-500 focus:border-blue-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleTheme,\n                                                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setNotificationsOpen(!notificationsOpen),\n                                                        className: \"notifications-button relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        title: \"Toggle notifications\",\n                                                        \"aria-label\": \"Toggle notifications\",\n                                                        \"aria-expanded\": notificationsOpen,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\",\n                                                                \"aria-label\": \"New notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    notificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"notifications-menu absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"Notifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-80 overflow-y-auto\",\n                                                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 rounded-full mt-2 \".concat(notification.unread ? 'bg-blue-500' : 'bg-gray-300')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 492,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: notification.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 494,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                                            children: notification.message\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 495,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                                            children: notification.time\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 496,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 493,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, notification.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setUserMenuOpen(!userMenuOpen),\n                                                        className: \"user-menu-button flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    userMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"user-menu absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Settings\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 525,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Help & Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 531,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                        className: \"my-2 border-gray-200 dark:border-gray-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 533,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 535,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Sign Out\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 sm:p-6 lg:p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 35\n                                    }, void 0),\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 sm:px-6 lg:px-8 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_ChevronRight_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"MedTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"\\xa9 2025 All rights reserved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mt-2 sm:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(MedTrackLayout, \"+Rb3zV2sCTVBQS6YAQiNEhnRBMs=\");\n_c1 = MedTrackLayout;\nconst RootLayout = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MedTrackLayout, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__.SyncStatusBanner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 587,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 586,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = RootLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RootLayout);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NavigationItem\");\n$RefreshReg$(_c1, \"MedTrackLayout\");\n$RefreshReg$(_c2, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"118357225d0f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXG1lZGljYWxcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMTgzNTcyMjVkMGZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});