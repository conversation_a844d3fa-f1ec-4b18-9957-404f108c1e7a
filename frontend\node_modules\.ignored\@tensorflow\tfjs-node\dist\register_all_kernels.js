"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
// We explicitly import the modular kernels so they get registered in the
// global registry when we compile the library. A modular build would replace
// the contents of this file and import only the kernels that are needed.
var tfjs_1 = require("@tensorflow/tfjs");
var _FusedMatMul_1 = require("./kernels/_FusedMatMul");
var Abs_1 = require("./kernels/Abs");
var Acos_1 = require("./kernels/Acos");
var Acosh_1 = require("./kernels/Acosh");
var Add_1 = require("./kernels/Add");
var AddN_1 = require("./kernels/AddN");
var All_1 = require("./kernels/All");
var Any_1 = require("./kernels/Any");
var ArgMax_1 = require("./kernels/ArgMax");
var ArgMin_1 = require("./kernels/ArgMin");
var Asin_1 = require("./kernels/Asin");
var Asinh_1 = require("./kernels/Asinh");
var Atan_1 = require("./kernels/Atan");
var Atan2_1 = require("./kernels/Atan2");
var Atanh_1 = require("./kernels/Atanh");
var AvgPool_1 = require("./kernels/AvgPool");
var AvgPool3D_1 = require("./kernels/AvgPool3D");
var AvgPool3DGrad_1 = require("./kernels/AvgPool3DGrad");
var AvgPoolGrad_1 = require("./kernels/AvgPoolGrad");
var BatchMatMul_1 = require("./kernels/BatchMatMul");
var BatchToSpaceND_1 = require("./kernels/BatchToSpaceND");
var Bincount_1 = require("./kernels/Bincount");
var BroadcastArgs_1 = require("./kernels/BroadcastArgs");
var Cast_1 = require("./kernels/Cast");
var Ceil_1 = require("./kernels/Ceil");
var ClipByValue_1 = require("./kernels/ClipByValue");
var Complex_1 = require("./kernels/Complex");
var ComplexAbs_1 = require("./kernels/ComplexAbs");
var Concat_1 = require("./kernels/Concat");
var Conv2D_1 = require("./kernels/Conv2D");
var Conv2DBackpropFilter_1 = require("./kernels/Conv2DBackpropFilter");
var Conv2DBackpropInput_1 = require("./kernels/Conv2DBackpropInput");
var Conv3D_1 = require("./kernels/Conv3D");
var Conv3DBackpropFilterV2_1 = require("./kernels/Conv3DBackpropFilterV2");
var Conv3DBackpropInputV2_1 = require("./kernels/Conv3DBackpropInputV2");
var Cos_1 = require("./kernels/Cos");
var Cosh_1 = require("./kernels/Cosh");
var CropAndResize_1 = require("./kernels/CropAndResize");
var Cumprod_1 = require("./kernels/Cumprod");
var Cumsum_1 = require("./kernels/Cumsum");
var DepthToSpace_1 = require("./kernels/DepthToSpace");
var DepthwiseConv2dNative_1 = require("./kernels/DepthwiseConv2dNative");
var DepthwiseConv2dNativeBackpropFilter_1 = require("./kernels/DepthwiseConv2dNativeBackpropFilter");
var DepthwiseConv2dNativeBackpropInput_1 = require("./kernels/DepthwiseConv2dNativeBackpropInput");
var Diag_1 = require("./kernels/Diag");
var Dilation2D_1 = require("./kernels/Dilation2D");
var Dilation2DBackpropFilter_1 = require("./kernels/Dilation2DBackpropFilter");
var Dilation2DBackpropInput_1 = require("./kernels/Dilation2DBackpropInput");
var Einsum_1 = require("./kernels/Einsum");
var Elu_1 = require("./kernels/Elu");
var EluGrad_1 = require("./kernels/EluGrad");
var Equal_1 = require("./kernels/Equal");
var Erf_1 = require("./kernels/Erf");
var Exp_1 = require("./kernels/Exp");
var ExpandDims_1 = require("./kernels/ExpandDims");
var Expm1_1 = require("./kernels/Expm1");
var FFT_1 = require("./kernels/FFT");
var Fill_1 = require("./kernels/Fill");
var FlipLeftRight_1 = require("./kernels/FlipLeftRight");
var Floor_1 = require("./kernels/Floor");
var FloorDiv_1 = require("./kernels/FloorDiv");
var FusedBatchNorm_1 = require("./kernels/FusedBatchNorm");
var FusedConv2D_1 = require("./kernels/FusedConv2D");
var FusedDepthwiseConv2D_1 = require("./kernels/FusedDepthwiseConv2D");
var GatherNd_1 = require("./kernels/GatherNd");
var GatherV2_1 = require("./kernels/GatherV2");
var Greater_1 = require("./kernels/Greater");
var GreaterEqual_1 = require("./kernels/GreaterEqual");
var Identity_1 = require("./kernels/Identity");
var IFFT_1 = require("./kernels/IFFT");
var Imag_1 = require("./kernels/Imag");
var IsFinite_1 = require("./kernels/IsFinite");
var IsInf_1 = require("./kernels/IsInf");
var IsNan_1 = require("./kernels/IsNan");
var LeakyRelu_1 = require("./kernels/LeakyRelu");
var Less_1 = require("./kernels/Less");
var LessEqual_1 = require("./kernels/LessEqual");
var LinSpace_1 = require("./kernels/LinSpace");
var Log_1 = require("./kernels/Log");
var Log1p_1 = require("./kernels/Log1p");
var LogicalAnd_1 = require("./kernels/LogicalAnd");
var LogicalNot_1 = require("./kernels/LogicalNot");
var LogicalOr_1 = require("./kernels/LogicalOr");
var LRN_1 = require("./kernels/LRN");
var LRNGrad_1 = require("./kernels/LRNGrad");
var Max_1 = require("./kernels/Max");
var Maximum_1 = require("./kernels/Maximum");
var MaxPool_1 = require("./kernels/MaxPool");
var MaxPool3D_1 = require("./kernels/MaxPool3D");
var MaxPool3DGrad_1 = require("./kernels/MaxPool3DGrad");
var MaxPoolGrad_1 = require("./kernels/MaxPoolGrad");
var Mean_1 = require("./kernels/Mean");
var Min_1 = require("./kernels/Min");
var Minimum_1 = require("./kernels/Minimum");
var MirrorPad_1 = require("./kernels/MirrorPad");
var Mod_1 = require("./kernels/Mod");
var Multinomial_1 = require("./kernels/Multinomial");
var Multiply_1 = require("./kernels/Multiply");
var Neg_1 = require("./kernels/Neg");
var NonMaxSuppressionV3_1 = require("./kernels/NonMaxSuppressionV3");
var NonMaxSuppressionV4_1 = require("./kernels/NonMaxSuppressionV4");
var NonMaxSuppressionV5_1 = require("./kernels/NonMaxSuppressionV5");
var NotEqual_1 = require("./kernels/NotEqual");
var OneHot_1 = require("./kernels/OneHot");
var OnesLike_1 = require("./kernels/OnesLike");
var Pack_1 = require("./kernels/Pack");
var PadV2_1 = require("./kernels/PadV2");
var Pow_1 = require("./kernels/Pow");
var Prelu_1 = require("./kernels/Prelu");
var Prod_1 = require("./kernels/Prod");
var Range_1 = require("./kernels/Range");
var Real_1 = require("./kernels/Real");
var RealDiv_1 = require("./kernels/RealDiv");
var Reciprocal_1 = require("./kernels/Reciprocal");
var Relu_1 = require("./kernels/Relu");
var Relu6_1 = require("./kernels/Relu6");
var Reshape_1 = require("./kernels/Reshape");
var ResizeBilinear_1 = require("./kernels/ResizeBilinear");
var ResizeBilinearGrad_1 = require("./kernels/ResizeBilinearGrad");
var ResizeNearestNeighbor_1 = require("./kernels/ResizeNearestNeighbor");
var ResizeNearestNeighborGrad_1 = require("./kernels/ResizeNearestNeighborGrad");
var Reverse_1 = require("./kernels/Reverse");
var Round_1 = require("./kernels/Round");
var Rsqrt_1 = require("./kernels/Rsqrt");
var ScatterNd_1 = require("./kernels/ScatterNd");
var Select_1 = require("./kernels/Select");
var Selu_1 = require("./kernels/Selu");
var Sigmoid_1 = require("./kernels/Sigmoid");
var Sign_1 = require("./kernels/Sign");
var Sin_1 = require("./kernels/Sin");
var Sinh_1 = require("./kernels/Sinh");
var Slice_1 = require("./kernels/Slice");
var Softmax_1 = require("./kernels/Softmax");
var Softplus_1 = require("./kernels/Softplus");
var SpaceToBatchND_1 = require("./kernels/SpaceToBatchND");
var SparseToDense_1 = require("./kernels/SparseToDense");
var SplitV_1 = require("./kernels/SplitV");
var Sqrt_1 = require("./kernels/Sqrt");
var Square_1 = require("./kernels/Square");
var SquaredDifference_1 = require("./kernels/SquaredDifference");
var StaticRegexReplace_1 = require("./kernels/StaticRegexReplace");
var Step_1 = require("./kernels/Step");
var StridedSlice_1 = require("./kernels/StridedSlice");
var Sub_1 = require("./kernels/Sub");
var Sum_1 = require("./kernels/Sum");
var Tan_1 = require("./kernels/Tan");
var Tanh_1 = require("./kernels/Tanh");
var TensorScatterUpdate_1 = require("./kernels/TensorScatterUpdate");
var Tile_1 = require("./kernels/Tile");
var TopK_1 = require("./kernels/TopK");
var Transpose_1 = require("./kernels/Transpose");
var Unique_1 = require("./kernels/Unique");
var Unpack_1 = require("./kernels/Unpack");
var UnsortedSegmentSum_1 = require("./kernels/UnsortedSegmentSum");
var ZerosLike_1 = require("./kernels/ZerosLike");
// List all kernel configs here
var kernelConfigs = [
    FFT_1.FFTConfig,
    IFFT_1.IFFTConfig,
    LRN_1.LRNConfig,
    LRNGrad_1.LRNGradConfig,
    _FusedMatMul_1._fusedMatMulConfig,
    Abs_1.absConfig,
    Acos_1.acosConfig,
    Acosh_1.acoshConfig,
    Add_1.addConfig,
    AddN_1.addNConfig,
    All_1.allConfig,
    Any_1.anyConfig,
    ArgMax_1.argMaxConfig,
    ArgMin_1.argMinConfig,
    Asin_1.asinConfig,
    Asinh_1.asinhConfig,
    Atan2_1.atan2Config,
    Atan_1.atanConfig,
    Atanh_1.atanhConfig,
    AvgPool3D_1.avgPool3DConfig,
    AvgPool3DGrad_1.avgPool3DGradConfig,
    AvgPool_1.avgPoolConfig,
    AvgPoolGrad_1.avgPoolGradConfig,
    BatchMatMul_1.batchMatMulConfig,
    BatchToSpaceND_1.batchToSpaceNDConfig,
    BroadcastArgs_1.broadcastArgsConfig,
    Cast_1.castConfig,
    Ceil_1.ceilConfig,
    ClipByValue_1.clipByValueConfig,
    ComplexAbs_1.complexAbsConfig,
    Complex_1.complexConfig,
    Concat_1.concatConfig,
    Conv2DBackpropFilter_1.conv2DBackpropFilterConfig,
    Conv2DBackpropInput_1.conv2DBackpropInputConfig,
    Conv2D_1.conv2DConfig,
    Conv3DBackpropFilterV2_1.conv3DBackpropFilterV2Config,
    Conv3DBackpropInputV2_1.conv3DBackpropInputV2Config,
    Conv3D_1.conv3DConfig,
    Cos_1.cosConfig,
    Cosh_1.coshConfig,
    CropAndResize_1.cropAndResizeConfig,
    Cumprod_1.cumprodConfig,
    Cumsum_1.cumsumConfig,
    Bincount_1.bincountConfig,
    DepthToSpace_1.depthToSpaceConfig,
    DepthwiseConv2dNativeBackpropFilter_1.depthwiseConv2dNativeBackpropFilterConfig,
    DepthwiseConv2dNativeBackpropInput_1.depthwiseConv2dNativeBackpropInputConfig,
    DepthwiseConv2dNative_1.depthwiseConv2dNativeConfig,
    Diag_1.diagConfig,
    Dilation2DBackpropFilter_1.dilation2dBackpropFilterConfig,
    Dilation2DBackpropInput_1.dilation2dBackpropInputConfig,
    Dilation2D_1.dilation2dConfig,
    Elu_1.eluConfig,
    EluGrad_1.eluGradConfig,
    Einsum_1.einsumConfig,
    Equal_1.equalConfig,
    Erf_1.erfConfig,
    Exp_1.expConfig,
    ExpandDims_1.expandDimsConfig,
    Expm1_1.expm1Config,
    Fill_1.fillConfig,
    FlipLeftRight_1.flipLeftRightConfig,
    Floor_1.floorConfig,
    FloorDiv_1.floorDivConfig,
    FusedBatchNorm_1.fusedBatchNormConfig,
    FusedConv2D_1.fusedConv2DConfig,
    FusedDepthwiseConv2D_1.fusedDepthwiseConv2DConfig,
    GatherNd_1.gatherNdConfig,
    GatherV2_1.gatherV2Config,
    Greater_1.greaterConfig,
    GreaterEqual_1.greaterEqualConfig,
    Identity_1.identityConfig,
    Imag_1.imagConfig,
    IsFinite_1.isFiniteConfig,
    IsInf_1.isInfConfig,
    IsNan_1.isNanConfig,
    LeakyRelu_1.leakyReluConfig,
    Less_1.lessConfig,
    LessEqual_1.lessEqualConfig,
    LinSpace_1.linSpaceConfig,
    Log1p_1.log1pConfig,
    Log_1.logConfig,
    LogicalAnd_1.logicalAndConfig,
    LogicalNot_1.logicalNotConfig,
    LogicalOr_1.logicalOrConfig,
    Max_1.maxConfig,
    MaxPool3D_1.maxPool3DConfig,
    MaxPool3DGrad_1.maxPool3DGradConfig,
    MaxPool_1.maxPoolConfig,
    MaxPoolGrad_1.maxPoolGradConfig,
    Maximum_1.maximumConfig,
    Mean_1.meanConfig,
    Min_1.minConfig,
    Minimum_1.minimumConfig,
    MirrorPad_1.mirrorPadConfig,
    Mod_1.modConfig,
    Multinomial_1.multinomialConfig,
    Multiply_1.multiplyConfig,
    Neg_1.negConfig,
    NonMaxSuppressionV3_1.nonMaxSuppressionV3Config,
    NonMaxSuppressionV4_1.nonMaxSuppressionV4Config,
    NonMaxSuppressionV5_1.nonMaxSuppressionV5Config,
    NotEqual_1.notEqualConfig,
    OneHot_1.oneHotConfig,
    OnesLike_1.onesLikeConfig,
    Pack_1.packConfig,
    PadV2_1.padV2Config,
    Pow_1.powConfig,
    Prelu_1.preluConfig,
    Prod_1.prodConfig,
    Range_1.rangeConfig,
    Real_1.realConfig,
    RealDiv_1.realDivConfig,
    Reciprocal_1.reciprocalConfig,
    Relu6_1.relu6Config,
    Relu_1.reluConfig,
    Reshape_1.reshapeConfig,
    ResizeBilinear_1.resizeBilinearConfig,
    ResizeBilinearGrad_1.resizeBilinearGradConfig,
    ResizeNearestNeighbor_1.resizeNearestNeighborConfig,
    ResizeNearestNeighborGrad_1.resizeNearestNeighborGradConfig,
    Reverse_1.reverseConfig,
    Round_1.roundConfig,
    Rsqrt_1.rsqrtConfig,
    ScatterNd_1.scatterNdConfig,
    Select_1.selectConfig,
    Selu_1.seluConfig,
    Sigmoid_1.sigmoidConfig,
    Sign_1.signConfig,
    Sin_1.sinConfig,
    Sinh_1.sinhConfig,
    Slice_1.sliceConfig,
    Softmax_1.softmaxConfig,
    Softplus_1.softplusConfig,
    SpaceToBatchND_1.spaceToBatchNDConfig,
    SparseToDense_1.sparseToDenseConfig,
    SplitV_1.splitVConfig,
    Sqrt_1.sqrtConfig,
    Square_1.squareConfig,
    SquaredDifference_1.squaredDifferenceConfig,
    StaticRegexReplace_1.staticRegexReplaceConfig,
    Step_1.stepConfig,
    StridedSlice_1.stridedSliceConfig,
    Sub_1.subConfig,
    Sum_1.sumConfig,
    Tan_1.tanConfig,
    Tanh_1.tanhConfig,
    TensorScatterUpdate_1.tensorScatterUpdateConfig,
    Tile_1.tileConfig,
    TopK_1.topKConfig,
    Transpose_1.transposeConfig,
    Unique_1.uniqueConfig,
    Unpack_1.unpackConfig,
    UnsortedSegmentSum_1.unsortedSegmentSumConfig,
    ZerosLike_1.zerosLikeConfig
];
for (var _i = 0, kernelConfigs_1 = kernelConfigs; _i < kernelConfigs_1.length; _i++) {
    var kernelConfig = kernelConfigs_1[_i];
    (0, tfjs_1.registerKernel)(kernelConfig);
}
