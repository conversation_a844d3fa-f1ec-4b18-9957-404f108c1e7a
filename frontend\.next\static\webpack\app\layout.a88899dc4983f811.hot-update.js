"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Stethoscope!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\");\n/* harmony import */ var _components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SyncStatusBanner */ \"(app-pages-browser)/./src/components/SyncStatusBanner.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/logger */ \"(app-pages-browser)/./src/lib/logger.ts\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/common/LoadingSpinner */ \"(app-pages-browser)/./src/components/common/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n// This file defines the main layout for the MedTrack application, including the sidebar, top navigation, and main content area.\n\n\n\n\n\n\n\n\n\n// Navigation Item Component\nconst NavigationItem = (param)=>{\n    let { item } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleClick = ()=>{\n        if (item.children) {\n            setIsExpanded(!isExpanded);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                onClick: handleClick,\n                className: \"w-full flex items-center justify-between px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined),\n                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",\n                                children: item.badge\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined),\n                    item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronRight, {\n                        className: \"h-4 w-4 transition-transform \".concat(isExpanded ? 'rotate-90' : '')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined),\n            item.children && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-6 mt-1 space-y-1\",\n                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                        item: child\n                    }, child.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NavigationItem, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c = NavigationItem;\nconst MedTrackLayout = (param)=>{\n    let { children } = param;\n    _s1();\n    // State management\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userMenuOpen, setUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationsOpen, setNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'learning'\n    ]);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Mock user data\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '1',\n        name: 'Dr. Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'Medical Student',\n        isAuthenticated: true\n    });\n    // Navigation structure\n    const navigationItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: Home,\n            href: '/dashboard'\n        },\n        {\n            id: 'learning',\n            label: 'Learning',\n            icon: GraduationCap,\n            href: '/learning',\n            children: [\n                {\n                    id: 'courses',\n                    label: 'My Courses',\n                    icon: BookOpen,\n                    href: '/courses',\n                    badge: 3\n                },\n                {\n                    id: 'progress',\n                    label: 'Progress Tracking',\n                    icon: BarChart3,\n                    href: '/progress'\n                },\n                {\n                    id: 'schedule',\n                    label: 'Study Schedule',\n                    icon: Calendar,\n                    href: '/schedule'\n                },\n                {\n                    id: 'quizzes',\n                    label: 'Quizzes & Tests',\n                    icon: Target,\n                    href: '/quizzes',\n                    badge: 2\n                }\n            ]\n        },\n        {\n            id: 'medical',\n            label: 'Medical Resources',\n            icon: _barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: '/medical',\n            children: [\n                {\n                    id: 'anatomy',\n                    label: 'Anatomy',\n                    icon: Activity,\n                    href: '/medical/anatomy'\n                },\n                {\n                    id: 'cardiology',\n                    label: 'Cardiology',\n                    icon: Heart,\n                    href: '/medical/cardiology'\n                },\n                {\n                    id: 'neurology',\n                    label: 'Neurology',\n                    icon: Brain,\n                    href: '/medical/neurology'\n                }\n            ]\n        },\n        {\n            id: 'community',\n            label: 'Community',\n            icon: Users,\n            href: '/community',\n            children: [\n                {\n                    id: 'discussions',\n                    label: 'Discussions',\n                    icon: Users,\n                    href: '/community/discussions'\n                },\n                {\n                    id: 'study-groups',\n                    label: 'Study Groups',\n                    icon: Users,\n                    href: '/community/study-groups'\n                }\n            ]\n        },\n        {\n            id: 'resources',\n            label: 'Resources',\n            icon: FileText,\n            href: '/resources'\n        },\n        {\n            id: 'achievements',\n            label: 'Achievements',\n            icon: Award,\n            href: '/achievements',\n            badge: 'New'\n        },\n        {\n            id: 'admin',\n            label: 'Admin',\n            icon: Settings,\n            href: '/admin',\n            children: [\n                {\n                    id: 'users',\n                    label: 'User Management',\n                    icon: Users,\n                    href: '/admin/users'\n                },\n                {\n                    id: 'roles',\n                    label: 'Role Management',\n                    icon: Shield,\n                    href: '/admin/roles'\n                },\n                {\n                    id: 'analytics',\n                    label: 'Analytics',\n                    icon: BarChart3,\n                    href: '/admin/analytics'\n                },\n                {\n                    id: 'settings',\n                    label: 'System Settings',\n                    icon: Settings,\n                    href: '/admin/settings'\n                }\n            ]\n        }\n    ];\n    // Mock notifications\n    const notifications = [\n        {\n            id: '1',\n            title: 'New Course Available',\n            message: 'Advanced Cardiology module is now live',\n            time: '5 min ago',\n            unread: true\n        },\n        {\n            id: '2',\n            title: 'Assignment Due',\n            message: 'Anatomy quiz due tomorrow',\n            time: '2 hours ago',\n            unread: true\n        },\n        {\n            id: '3',\n            title: 'Study Group Meeting',\n            message: 'Cardiology study group starts in 1 hour',\n            time: '1 day ago',\n            unread: false\n        }\n    ];\n    // Theme toggle\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'light' ? 'dark' : 'light');\n    };\n    // Sidebar section toggle\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>prev.includes(sectionId) ? prev.filter((id)=>id !== sectionId) : [\n                ...prev,\n                sectionId\n            ]);\n    };\n    // Handle outside clicks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"MedTrackLayout.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (!target.closest('.user-menu') && !target.closest('.user-menu-button')) {\n                        setUserMenuOpen(false);\n                    }\n                    if (!target.closest('.notifications-menu') && !target.closest('.notifications-button')) {\n                        setNotificationsOpen(false);\n                    }\n                }\n            }[\"MedTrackLayout.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // Simulate loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"MedTrackLayout.useEffect.timer\": ()=>setIsLoading(false)\n            }[\"MedTrackLayout.useEffect.timer\"], 1000);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // ServiceWorker registration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"MedTrackLayout.useEffect\": (_registration)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.info('ServiceWorker registration successful');\n                    }\n                }[\"MedTrackLayout.useEffect\"]).catch({\n                    \"MedTrackLayout.useEffect\": (err)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.error('ServiceWorker registration failed:', err);\n                    }\n                }[\"MedTrackLayout.useEffect\"]);\n            }\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"\".concat(sidebarOpen ? 'block' : 'hidden', \" lg:block\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"lg:hidden fixed top-4 left-4 z-20\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            \"aria-expanded\": String(sidebarOpen),\n                            \"aria-label\": \"Toggle sidebar navigation\",\n                            title: \"Toggle Navigation\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Menu, {\n                                className: \"h-6 w-6\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out\\n            \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full', \"\\n            lg:translate-x-0 lg:static lg:inset-0\\n            \").concat(sidebarCollapsed && !sidebarOpen ? 'lg:w-16' : 'w-64', \"\\n            \").concat(theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200', \"\\n            border-r flex flex-col\\n          \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: \"MedTrack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                            children: \"Medical Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: \"lg:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex-1 overflow-y-auto py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                                                item: item\n                                            }, item.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t border-gray-200 dark:border-gray-700\",\n                                    children: (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs truncate \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                        children: user.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"bg-white dark:bg-gray-800 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                        className: \"text-gray-500 hover:text-gray-600 dark:text-gray-400\",\n                                        \"aria-expanded\": String(!sidebarCollapsed),\n                                        \"aria-label\": \"Toggle sidebar collapse\",\n                                        title: \"Toggle Sidebar\",\n                                        children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Menu, {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search courses, topics...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 pr-4 py-2 w-64 rounded-lg border \".concat(theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500', \" focus:ring-2 focus:ring-blue-500 focus:border-blue-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleTheme,\n                                                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sun, {\n                                                    className: \"h-5 w-5 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Moon, {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setNotificationsOpen(!notificationsOpen),\n                                                        className: \"notifications-button relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        title: \"Toggle notifications\",\n                                                        \"aria-label\": \"Toggle notifications\",\n                                                        \"aria-expanded\": notificationsOpen,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Bell, {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\",\n                                                                \"aria-label\": \"New notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    notificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"notifications-menu absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"Notifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-80 overflow-y-auto\",\n                                                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 rounded-full mt-2 \".concat(notification.unread ? 'bg-blue-500' : 'bg-gray-300')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 467,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: notification.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 469,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                                            children: notification.message\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 470,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                                            children: notification.time\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 471,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 468,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                            lineNumber: 466,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, notification.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setUserMenuOpen(!userMenuOpen),\n                                                        className: \"user-menu-button flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                                                    className: \"h-4 w-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronDown, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    userMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"user-menu absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Settings, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Settings\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HelpCircle, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Help & Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                        className: \"my-2 border-gray-200 dark:border-gray-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogOut, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 511,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Sign Out\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 sm:p-6 lg:p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 35\n                                    }, void 0),\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 sm:px-6 lg:px-8 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"MedTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"\\xa9 2025 All rights reserved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mt-2 sm:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(MedTrackLayout, \"+Rb3zV2sCTVBQS6YAQiNEhnRBMs=\");\n_c1 = MedTrackLayout;\nconst RootLayout = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MedTrackLayout, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__.SyncStatusBanner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 9\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 563,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 562,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = RootLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RootLayout);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NavigationItem\");\n$RefreshReg$(_c1, \"MedTrackLayout\");\n$RefreshReg$(_c2, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6b70ee8664b3\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXG1lZGljYWxcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2YjcwZWU4NjY0YjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});