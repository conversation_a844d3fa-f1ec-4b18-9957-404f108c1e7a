{"version": 3, "file": "modern-module-resolution.js", "sourceRoot": "", "sources": ["../src/modern-module-resolution.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,wEAAwE;AACxE,EAAE;AACF,oHAAoH;AACpH,EAAE;AACF,kEAAkE;AAClE,EAAE;AAEF,+CAKuB;AAEvB,oHAAoH;AACpH,MAAM,qBAAqB,GAA6B,CAAC,EAAE,EAAE,EAAE,CAC7D,CAAC,EAAoC,aAApC,EAAE,uBAAF,EAAE,CAAoC,IAAI,MAAK,uBAAuB,CAAC;AAE1E,IAAI,CAAC,gCAAkB,CAAC,mBAAmB,EAAE,CAAC;IAC5C,gCAAkB,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAC9C,sDAAsD;IACtD,MAAM,kBAAkB,GAAG,gCAAkB,CAAC,SAAS,CAAC,WAAW,CAAC;IAEpE,IAAI,kCAAoB,KAAK,CAAC,EAAE,CAAC;QAC/B,aAAa;QACb,6HAA6H;QAC7H,gCAAkB,CAAC,SAAS,CAAC,WAAW,GAAG,UACzC,IAAY,EACZ,YAAoB,EACpB,YAAoB;YAEpB,MAAM,eAAe,GAA2D,4BAAc,CAAC,OAAO,CAAC;YACvG,IAAI,CAAC;gBACH,4BAAc,CAAC,OAAO,GAAG,UAAU,UAAkB,EAAE,cAAsB;oBAC3E,IAAI,CAAC;wBACH,uDAAuD;wBACvD,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;oBAC9D,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,IAAI,IAAA,qCAAuB,EAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC;4BAC3D,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;wBAChE,CAAC;wBACD,MAAM,CAAC,CAAC;oBACV,CAAC;gBACH,CAAC,CAAC;gBACF,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACnD,CAAC;oBAAS,CAAC;gBACT,4BAAc,CAAC,OAAO,GAAG,eAAe,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,oBAAoB;QACpB,qHAAqH;QACrH,gCAAkB,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,IAAY,EAAE,GAA4B;YAC7F,MAAM,eAAe,GACnB,4BAAc,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC;gBACH,4BAAc,CAAC,OAAO,GAAG,UAAU,UAAkB,EAAE,cAAsB;oBAC3E,IAAI,CAAC;wBACH,uDAAuD;wBACvD,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC9D,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,IAAI,IAAA,qCAAuB,EAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC;4BAC3D,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;wBAChE,CAAC;wBACD,MAAM,CAAC,CAAC;oBACV,CAAC;gBACH,CAAC,CAAC;gBACF,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACnD,CAAC;oBAAS,CAAC;gBACT,4BAAc,CAAC,OAAO,GAAG,eAAe,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\n// This is a workaround for https://github.com/eslint/eslint/issues/3458\n//\n// To correct how ESLint searches for plugin packages, add this line to the top of your project's .eslintrc.js file:\n//\n//    require(\"@rushstack/eslint-patch/modern-module-resolution\");\n//\n\nimport {\n  configArrayFactory,\n  ModuleResolver,\n  isModuleResolutionError,\n  ESLINT_MAJOR_VERSION\n} from './_patch-base';\n\n// error: \"The argument 'filename' must be a file URL object, file URL string, or absolute path string. Received ''\"\nconst isInvalidImporterPath: (ex: unknown) => boolean = (ex) =>\n  (ex as { code: unknown } | undefined)?.code === 'ERR_INVALID_ARG_VALUE';\n\nif (!configArrayFactory.__loadPluginPatched) {\n  configArrayFactory.__loadPluginPatched = true;\n  // eslint-disable-next-line @typescript-eslint/typedef\n  const originalLoadPlugin = configArrayFactory.prototype._loadPlugin;\n\n  if (ESLINT_MAJOR_VERSION === 6) {\n    // ESLint 6.x\n    // https://github.com/eslint/eslint/blob/9738f8cc864d769988ccf42bb70f524444df1349/lib/cli-engine/config-array-factory.js#L915\n    configArrayFactory.prototype._loadPlugin = function (\n      name: string,\n      importerPath: string,\n      importerName: string\n    ) {\n      const originalResolve: (moduleName: string, relativeToPath: string) => string = ModuleResolver.resolve;\n      try {\n        ModuleResolver.resolve = function (moduleName: string, relativeToPath: string) {\n          try {\n            // resolve using importerPath instead of relativeToPath\n            return originalResolve.call(this, moduleName, importerPath);\n          } catch (e) {\n            if (isModuleResolutionError(e) || isInvalidImporterPath(e)) {\n              return originalResolve.call(this, moduleName, relativeToPath);\n            }\n            throw e;\n          }\n        };\n        return originalLoadPlugin.apply(this, arguments);\n      } finally {\n        ModuleResolver.resolve = originalResolve;\n      }\n    };\n  } else {\n    // ESLint 7.x || 8.x\n    // https://github.com/eslint/eslintrc/blob/242d569020dfe4f561e4503787b99ec016337457/lib/config-array-factory.js#L1023\n    configArrayFactory.prototype._loadPlugin = function (name: string, ctx: Record<string, unknown>) {\n      const originalResolve: (moduleName: string, relativeToPath: string | unknown) => string =\n        ModuleResolver.resolve;\n      try {\n        ModuleResolver.resolve = function (moduleName: string, relativeToPath: string) {\n          try {\n            // resolve using ctx.filePath instead of relativeToPath\n            return originalResolve.call(this, moduleName, ctx.filePath);\n          } catch (e) {\n            if (isModuleResolutionError(e) || isInvalidImporterPath(e)) {\n              return originalResolve.call(this, moduleName, relativeToPath);\n            }\n            throw e;\n          }\n        };\n        return originalLoadPlugin.apply(this, arguments);\n      } finally {\n        ModuleResolver.resolve = originalResolve;\n      }\n    };\n  }\n}\n"]}