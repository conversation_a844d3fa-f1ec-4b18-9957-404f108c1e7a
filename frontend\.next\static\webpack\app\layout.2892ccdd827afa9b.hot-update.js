"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,BookOpen,Brain,Calendar,ChevronDown,FileText,GraduationCap,Heart,HelpCircle,Home,LogOut,Menu,Moon,Search,Settings,Shield,Stethoscope,Sun,Target,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.323.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\");\n/* harmony import */ var _components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SyncStatusBanner */ \"(app-pages-browser)/./src/components/SyncStatusBanner.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/logger */ \"(app-pages-browser)/./src/lib/logger.ts\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/common/LoadingSpinner */ \"(app-pages-browser)/./src/components/common/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// This file defines the main layout for the MedTrack application, including the sidebar, top navigation, and main content area.\n\n\n\n\n\n\n\n\n\nconst MedTrackLayout = (param)=>{\n    let { children } = param;\n    _s();\n    // State management\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userMenuOpen, setUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationsOpen, setNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'learning'\n    ]);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Mock user data\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '1',\n        name: 'Dr. Sarah Johnson',\n        email: '<EMAIL>',\n        role: 'Medical Student',\n        isAuthenticated: true\n    });\n    // Navigation structure\n    const navigationItems = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: '/dashboard'\n        },\n        {\n            id: 'learning',\n            label: 'Learning',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: '/learning',\n            children: [\n                {\n                    id: 'courses',\n                    label: 'My Courses',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    href: '/courses',\n                    badge: 3\n                },\n                {\n                    id: 'progress',\n                    label: 'Progress Tracking',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: '/progress'\n                },\n                {\n                    id: 'schedule',\n                    label: 'Study Schedule',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    href: '/schedule'\n                },\n                {\n                    id: 'quizzes',\n                    label: 'Quizzes & Tests',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    href: '/quizzes',\n                    badge: 2\n                }\n            ]\n        },\n        {\n            id: 'medical',\n            label: 'Medical Resources',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            href: '/medical',\n            children: [\n                {\n                    id: 'anatomy',\n                    label: 'Anatomy',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    href: '/medical/anatomy'\n                },\n                {\n                    id: 'cardiology',\n                    label: 'Cardiology',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    href: '/medical/cardiology'\n                },\n                {\n                    id: 'neurology',\n                    label: 'Neurology',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    href: '/medical/neurology'\n                }\n            ]\n        },\n        {\n            id: 'community',\n            label: 'Community',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            href: '/community',\n            children: [\n                {\n                    id: 'discussions',\n                    label: 'Discussions',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    href: '/community/discussions'\n                },\n                {\n                    id: 'study-groups',\n                    label: 'Study Groups',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    href: '/community/study-groups'\n                }\n            ]\n        },\n        {\n            id: 'resources',\n            label: 'Resources',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            href: '/resources'\n        },\n        {\n            id: 'achievements',\n            label: 'Achievements',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            href: '/achievements',\n            badge: 'New'\n        },\n        {\n            id: 'admin',\n            label: 'Admin',\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            href: '/admin',\n            children: [\n                {\n                    id: 'users',\n                    label: 'User Management',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    href: '/admin/users'\n                },\n                {\n                    id: 'roles',\n                    label: 'Role Management',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                    href: '/admin/roles'\n                },\n                {\n                    id: 'analytics',\n                    label: 'Analytics',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    href: '/admin/analytics'\n                },\n                {\n                    id: 'settings',\n                    label: 'System Settings',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                    href: '/admin/settings'\n                }\n            ]\n        }\n    ];\n    // Mock notifications\n    const notifications = [\n        {\n            id: '1',\n            title: 'New Course Available',\n            message: 'Advanced Cardiology module is now live',\n            time: '5 min ago',\n            unread: true\n        },\n        {\n            id: '2',\n            title: 'Assignment Due',\n            message: 'Anatomy quiz due tomorrow',\n            time: '2 hours ago',\n            unread: true\n        },\n        {\n            id: '3',\n            title: 'Study Group Meeting',\n            message: 'Cardiology study group starts in 1 hour',\n            time: '1 day ago',\n            unread: false\n        }\n    ];\n    // Theme toggle\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'light' ? 'dark' : 'light');\n    };\n    // Sidebar section toggle\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>prev.includes(sectionId) ? prev.filter((id)=>id !== sectionId) : [\n                ...prev,\n                sectionId\n            ]);\n    };\n    // Handle outside clicks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"MedTrackLayout.useEffect.handleClickOutside\": (event)=>{\n                    const target = event.target;\n                    if (!target.closest('.user-menu') && !target.closest('.user-menu-button')) {\n                        setUserMenuOpen(false);\n                    }\n                    if (!target.closest('.notifications-menu') && !target.closest('.notifications-button')) {\n                        setNotificationsOpen(false);\n                    }\n                }\n            }[\"MedTrackLayout.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // Simulate loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"MedTrackLayout.useEffect.timer\": ()=>setIsLoading(false)\n            }[\"MedTrackLayout.useEffect.timer\"], 1000);\n            return ({\n                \"MedTrackLayout.useEffect\": ()=>clearTimeout(timer)\n            })[\"MedTrackLayout.useEffect\"];\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    // ServiceWorker registration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedTrackLayout.useEffect\": ()=>{\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"MedTrackLayout.useEffect\": (_registration)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.info('ServiceWorker registration successful');\n                    }\n                }[\"MedTrackLayout.useEffect\"]).catch({\n                    \"MedTrackLayout.useEffect\": (err)=>{\n                        _lib_logger__WEBPACK_IMPORTED_MODULE_6__.logger.error('ServiceWorker registration failed:', err);\n                    }\n                }[\"MedTrackLayout.useEffect\"]);\n            }\n        }\n    }[\"MedTrackLayout.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 299,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"\".concat(sidebarOpen ? 'block' : 'hidden', \" lg:block\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"lg:hidden fixed top-4 left-4 z-20\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            \"aria-expanded\": String(sidebarOpen),\n                            \"aria-label\": \"Toggle sidebar navigation\",\n                            title: \"Toggle Navigation\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"h-6 w-6\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out\\n            \".concat(sidebarOpen ? 'translate-x-0' : '-translate-x-full', \"\\n            lg:translate-x-0 lg:static lg:inset-0\\n            \").concat(sidebarCollapsed && !sidebarOpen ? 'lg:w-16' : 'w-64', \"\\n            \").concat(theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200', \"\\n            border-r flex flex-col\\n          \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: \"MedTrack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                            children: \"Medical Education\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: \"lg:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex-1 overflow-y-auto py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationItem, {\n                                                item: item\n                                            }, item.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t border-gray-200 dark:border-gray-700\",\n                                    children: (!sidebarCollapsed || sidebarOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs truncate \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500'),\n                                                        children: user.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"bg-white dark:bg-gray-800 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                        className: \"text-gray-500 hover:text-gray-600 dark:text-gray-400\",\n                                        \"aria-expanded\": String(!sidebarCollapsed),\n                                        \"aria-label\": \"Toggle sidebar collapse\",\n                                        title: \"Toggle Sidebar\",\n                                        children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-6 w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 \".concat(theme === 'dark' ? 'text-gray-400' : 'text-gray-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search courses, topics...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 pr-4 py-2 w-64 rounded-lg border \".concat(theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500', \" focus:ring-2 focus:ring-blue-500 focus:border-blue-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleTheme,\n                                                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setNotificationsOpen(!notificationsOpen),\n                                                        className: \"notifications-button relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        title: \"Toggle notifications\",\n                                                        \"aria-label\": \"Toggle notifications\",\n                                                        \"aria-expanded\": notificationsOpen,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\",\n                                                                \"aria-label\": \"New notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    notificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"notifications-menu absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"Notifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-80 overflow-y-auto\",\n                                                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 rounded-full mt-2 \".concat(notification.unread ? 'bg-blue-500' : 'bg-gray-300')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: notification.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 449,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                                            children: notification.message\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 450,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                                            children: notification.time\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                            lineNumber: 451,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                    lineNumber: 448,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, notification.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setUserMenuOpen(!userMenuOpen),\n                                                        className: \"user-menu-button flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    userMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"user-menu absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 481,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Settings\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Help & Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                        className: \"my-2 border-gray-200 dark:border-gray-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-left text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 490,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Sign Out\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                                lineNumber: 491,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 sm:p-6 lg:p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinner, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 35\n                                    }, void 0),\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 sm:px-6 lg:px-8 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_BookOpen_Brain_Calendar_ChevronDown_FileText_GraduationCap_Heart_HelpCircle_Home_LogOut_Menu_Moon_Search_Settings_Shield_Stethoscope_Sun_Target_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"MedTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"\\xa9 2025 All rights reserved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mt-2 sm:mt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm hover:underline text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MedTrackLayout, \"+Rb3zV2sCTVBQS6YAQiNEhnRBMs=\");\n_c = MedTrackLayout;\nconst RootLayout = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MedTrackLayout, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SyncStatusBanner__WEBPACK_IMPORTED_MODULE_4__.SyncStatusBanner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 544,\n                    columnNumber: 9\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 542,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\medical\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 541,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = RootLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RootLayout);\nvar _c, _c1;\n$RefreshReg$(_c, \"MedTrackLayout\");\n$RefreshReg$(_c1, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e9f104250218\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXG1lZGljYWxcXGZyb250ZW5kXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlOWYxMDQyNTAyMThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

});